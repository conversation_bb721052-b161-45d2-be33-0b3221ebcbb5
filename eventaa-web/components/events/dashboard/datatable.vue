<template>
  <div class="bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden">
    <!-- Table Header -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center">
            <input
              type="checkbox"
              :checked="allSelected"
              @change="toggleSelectAll"
              class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
            />
            <span class="ml-3 text-sm font-medium text-gray-700">
              {{ selectedCount > 0 ? `${selectedCount} selected` : 'Select all' }}
            </span>
          </div>
          <button
            v-if="selectedCount > 0"
            @click="publishSelected"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <Icon icon="heroicons:eye" class="w-4 h-4 mr-1" />
            Publish selected
          </button>
          <button
            v-if="selectedCount > 0"
            @click="deleteSelected"
            class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <Icon icon="heroicons:trash" class="w-4 h-4 mr-1" />
            Delete selected
          </button>
        </div>
        <div class="text-sm text-gray-500">
          {{ totalItems }} entries
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-20">
      <CoreLoader />
    </div>

    <!-- Table Content -->
    <div v-else class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="w-4 px-6 py-3">
              <!-- Checkbox column header -->
            </th>
            <th
              v-for="header in tableHeaders"
              :key="header.value"
              scope="col"
              class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
              @click="handleSort(header.value)"
            >
              <div class="flex items-center space-x-1">
                <span>{{ header.text }}</span>
                <Icon
                  v-if="header.sortable"
                  :icon="getSortIcon(header.value)"
                  class="w-4 h-4 text-gray-400"
                />
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr
            v-for="(event, index) in events"
            :key="event.id"
            class="hover:bg-gray-50 transition-colors"
            :class="{ 'bg-blue-50': isSelected(event.id) }"
          >
            <!-- Checkbox Column -->
            <td class="px-6 py-4 whitespace-nowrap">
              <input
                type="checkbox"
                :checked="isSelected(event.id)"
                @change="toggleSelect(event.id)"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
            </td>

            <!-- Event Title -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">
                {{ event.title }}
              </div>
            </td>

            <!-- Description -->
            <td class="px-6 py-4">
              <div class="text-sm text-gray-500 max-w-xs truncate" v-html="event.description"></div>
            </td>

            <!-- Location -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ event.location }}</div>
            </td>

            <!-- Start Date -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ event.start }}</div>
            </td>

            <!-- End Date -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ event.end }}</div>
            </td>

            <!-- Status -->
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                :class="getStatusClass(getEventStatus(event))"
              >
                {{ getEventStatus(event) }}
              </span>
            </td>

            <!-- Published At -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ event.published_at }}</div>
            </td>

            <!-- Created At -->
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">{{ event.created_at }}</div>
            </td>

            <!-- Actions -->
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex items-center justify-end space-x-2">
                <!-- Publish/Unpublish Button -->
                <button
                  v-if="getEventStatus(event) === 'Draft'"
                  @click="publishEvent(event)"
                  class="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                  title="Publish Event"
                >
                  <Icon icon="heroicons:eye" class="w-5 h-5" />
                </button>
                <button
                  v-else-if="getEventStatus(event) === 'Published' || getEventStatus(event) === 'Upcoming'"
                  @click="unpublishEvent(event)"
                  class="text-orange-600 hover:text-orange-900 p-1 rounded hover:bg-orange-50"
                  title="Unpublish Event"
                >
                  <Icon icon="heroicons:eye-slash" class="w-5 h-5" />
                </button>

                <!-- View Button -->
                <EventsDashboardView :event="event" />

                <!-- Edit Button -->
                <button
                  @click="navigateTo(`/dashboard/manage-events/edit/${event.slug}`)"
                  class="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                  title="Edit Event"
                >
                  <Icon icon="heroicons:pencil-square" class="w-5 h-5" />
                </button>

                <!-- Analytics Button -->
                <button
                  @click="viewAnalytics(event)"
                  class="text-purple-600 hover:text-purple-900 p-1 rounded hover:bg-purple-50"
                  title="View Analytics"
                >
                  <Icon icon="heroicons:chart-bar" class="w-5 h-5" />
                </button>

                <!-- Delete Button -->
                <EventsDeleteDialog :event="event" @onEventDeleted="onEventDeleted" />

                <!-- More Actions -->
                <div class="relative" ref="dropdownRef">
                  <button
                    @click="toggleDropdown(event.id)"
                    class="text-gray-400 hover:text-gray-600 p-1 rounded hover:bg-gray-50"
                    title="More actions"
                  >
                    <Icon icon="heroicons:ellipsis-vertical" class="w-5 h-5" />
                  </button>

                  <!-- Dropdown Menu -->
                  <div
                    v-if="activeDropdown === event.id"
                    class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200"
                  >
                    <div class="py-1">
                      <button
                        @click="duplicateEvent(event)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Icon icon="heroicons:document-duplicate" class="w-4 h-4 inline mr-2" />
                        Duplicate Event
                      </button>
                      <button
                        @click="viewBookings(event)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Icon icon="heroicons:ticket" class="w-4 h-4 inline mr-2" />
                        View Bookings
                      </button>
                      <button
                        @click="exportEvent(event)"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <Icon icon="heroicons:arrow-down-tray" class="w-4 h-4 inline mr-2" />
                        Export Data
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div v-if="!loading && events.length === 0" class="text-center py-12">
      <Icon icon="heroicons:calendar-days" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">No events found</h3>
      <p class="text-gray-500">Get started by creating your first event.</p>
    </div>

    <!-- Pagination -->
    <div v-if="!loading && events.length > 0" class="px-6 py-4 border-t border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Showing {{ startItem }} to {{ endItem }} of {{ totalItems }} results
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="previousPage"
            :disabled="currentPage <= 1"
            class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <span class="text-sm text-gray-700">
            {{ currentPage }} of {{ totalPages }}
          </span>
          <button
            @click="nextPage"
            :disabled="currentPage >= totalPages"
            class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { EventItem } from "@/types";
import type { Header } from "vue3-easy-data-table";

const emits = defineEmits(['update:serverOptions', 'update:options', 'update:events'])
const props = defineProps({
    headers: {
        type: Array as PropType<Header[]>,
        required: true,
    },
    events: {
        type: Array as PropType<EventItem[]>,
        required: true,
    },
    totalItems: {
        type: Number,
        required: true,
    },
    serverOptions: {
        required: false,
        type: Object,
        default: () => ({
            page: 1,
            rowsPerPage: 25,
        })
    },
    loading: {
        required: true,
        type: Boolean,
        default: false
    }
})

// Selection state
const selectedItems = ref<number[]>([])
const activeDropdown = ref<number | null>(null)
const dropdownRef = ref<HTMLElement | null>(null)

// Table headers (filtered to exclude actions)
const tableHeaders = computed(() => {
  return props.headers.filter((header: Header) => header.value !== 'actions')
})

// Sorting state
const sortBy = ref<string>('')
const sortOrder = ref<'asc' | 'desc'>('asc')

// Pagination computed properties
const currentPage = computed(() => props.serverOptions?.page || 1)
const rowsPerPage = computed(() => props.serverOptions?.rowsPerPage || 25)
const totalPages = computed(() => Math.ceil(props.totalItems / rowsPerPage.value))
const startItem = computed(() => (currentPage.value - 1) * rowsPerPage.value + 1)
const endItem = computed(() => Math.min(currentPage.value * rowsPerPage.value, props.totalItems))

// Selection computed properties
const selectedCount = computed(() => selectedItems.value.length)
const allSelected = computed(() => {
  return props.events.length > 0 && selectedItems.value.length === props.events.length
})

// Selection methods
const isSelected = (id: number): boolean => {
  return selectedItems.value.includes(id)
}

const toggleSelect = (id: number): void => {
  const index = selectedItems.value.indexOf(id)
  if (index > -1) {
    selectedItems.value.splice(index, 1)
  } else {
    selectedItems.value.push(id)
  }
}

const toggleSelectAll = (): void => {
  if (allSelected.value) {
    selectedItems.value = []
  } else {
    selectedItems.value = props.events.map((event: EventItem) => event.id)
  }
}

// Event status logic
const getEventStatus = (event: EventItem): string => {
  const now = new Date()
  const eventStart = new Date(event.start)
  const eventEnd = new Date(event.end)

  if (!event.published_at) return "Draft"
  if (eventEnd < now) return "Completed"
  if (eventStart > now) return "Upcoming"
  if (eventStart <= now && eventEnd >= now) return "Live"
  return "Published"
}

// Status styling
const getStatusClass = (status: string): string => {
  const statusClasses: Record<string, string> = {
    'Published': 'bg-green-100 text-green-800',
    'Draft': 'bg-yellow-100 text-yellow-800',
    'Cancelled': 'bg-red-100 text-red-800',
    'Completed': 'bg-gray-100 text-gray-800',
    'Upcoming': 'bg-blue-100 text-blue-800',
    'Live': 'bg-purple-100 text-purple-800'
  }
  return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Sorting methods
const handleSort = (column: string): void => {
  if (sortBy.value === column) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = column
    sortOrder.value = 'asc'
  }

  const newOptions = {
    ...props.serverOptions,
    sortBy: sortBy.value,
    sortType: sortOrder.value
  }
  emits('update:options', newOptions)
}

const getSortIcon = (column: string): string => {
  if (sortBy.value !== column) return 'heroicons:chevron-up-down'
  return sortOrder.value === 'asc' ? 'heroicons:chevron-up' : 'heroicons:chevron-down'
}

// Pagination methods
const previousPage = (): void => {
  if (currentPage.value > 1) {
    const newOptions = {
      ...props.serverOptions,
      page: currentPage.value - 1
    }
    emits('update:options', newOptions)
  }
}

const nextPage = (): void => {
  if (currentPage.value < totalPages.value) {
    const newOptions = {
      ...props.serverOptions,
      page: currentPage.value + 1
    }
    emits('update:options', newOptions)
  }
}

// Action methods
const publishEvent = (event: EventItem): void => {
  // TODO: Implement publish functionality
  console.log('Publish event:', event.id)
}

const unpublishEvent = (event: EventItem): void => {
  // TODO: Implement unpublish functionality
  console.log('Unpublish event:', event.id)
}

const viewAnalytics = (event: EventItem): void => {
  // TODO: Navigate to analytics page
  navigateTo(`/dashboard/analytics/events/${event.slug}`)
}

const viewBookings = (event: EventItem): void => {
  // TODO: Navigate to bookings page
  navigateTo(`/dashboard/events/${event.slug}/bookings`)
  activeDropdown.value = null
}

const publishSelected = (): void => {
  // TODO: Implement bulk publish functionality
  console.log('Publish selected events:', selectedItems.value)
  selectedItems.value = []
}

const deleteSelected = (): void => {
  // TODO: Implement bulk delete functionality
  console.log('Delete selected events:', selectedItems.value)
  selectedItems.value = []
}

const duplicateEvent = (event: EventItem): void => {
  // TODO: Implement duplicate functionality
  console.log('Duplicate event:', event.id)
  activeDropdown.value = null
}

const exportEvent = (event: EventItem): void => {
  // TODO: Implement export functionality
  console.log('Export event:', event.id)
  activeDropdown.value = null
}

const toggleDropdown = (eventId: number): void => {
  activeDropdown.value = activeDropdown.value === eventId ? null : eventId
}

const onEventDeleted = (value: boolean): void => {
  if (value) {
    emits('update:events', value)
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event: Event): void => {
  if (dropdownRef.value && dropdownRef.value.contains && dropdownRef.value.contains(event.target as Node)) {
    return
  }
  activeDropdown.value = null
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

watch(() => props.serverOptions, (n) => {
    if (n) {
        emits('update:serverOptions', n)
    }
}, { immediate: true })
</script>

<style scoped>
/* Custom scrollbar for table */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth transitions */
.transition-colors {
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
}

/* Table row hover effect */
tbody tr:hover {
  background-color: #f8fafc;
}

/* Selected row styling */
tbody tr.bg-blue-50 {
  background-color: #eff6ff !important;
}

tbody tr.bg-blue-50:hover {
  background-color: #dbeafe !important;
}
</style>
